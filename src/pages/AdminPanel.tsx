import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useUserRole } from '@/hooks/useUserRole';
import ScanlineEffect from '@/components/ScanlineEffect';
import UserMenu from '@/components/UserMenu';
import PaymentManagement from '@/components/PaymentManagement';
import TerminalWindow from '@/components/TerminalWindow';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  DollarSign, 
  Users, 
  Settings, 
  BarChart3, 
  Shield,
  Database,
  CreditCard
} from 'lucide-react';

const AdminPanel = () => {
  const { user } = useAuth();
  const { isAdmin } = useUserRole();
  const [activeTab, setActiveTab] = useState('payments');

  // Redirect non-admin users
  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-black text-retro-green font-mono flex items-center justify-center">
        <TerminalWindow title="ACCESS_DENIED.EXE">
          <div className="text-center space-y-4">
            <Shield className="w-16 h-16 mx-auto text-red-500" />
            <div className="text-red-500 font-bold">ACCESS DENIED</div>
            <div className="text-retro-green/70">
              Administrator privileges required to access this area.
            </div>
          </div>
        </TerminalWindow>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-retro-green font-mono relative overflow-hidden">
      <ScanlineEffect />
      
      {/* Navigation */}
      <nav className="relative z-10 border-b border-retro-green p-4">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="text-xl font-bold">
            ADMIN_PANEL.EXE
          </div>
          <UserMenu />
        </div>
      </nav>

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">
            &gt; ADMINISTRATION CONSOLE
          </h1>
          <p className="text-retro-green/80">
            Advanced system management and analytics
          </p>
        </div>

        {/* Admin Tabs */}
        <TerminalWindow title="ADMIN_MODULES.SYS">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-black border border-retro-green">
              <TabsTrigger 
                value="payments" 
                className="data-[state=active]:bg-retro-green data-[state=active]:text-black text-retro-green"
              >
                <CreditCard className="w-4 h-4 mr-2" />
                PAYMENTS
              </TabsTrigger>
              <TabsTrigger 
                value="users" 
                className="data-[state=active]:bg-retro-green data-[state=active]:text-black text-retro-green"
              >
                <Users className="w-4 h-4 mr-2" />
                USERS
              </TabsTrigger>
              <TabsTrigger 
                value="analytics" 
                className="data-[state=active]:bg-retro-green data-[state=active]:text-black text-retro-green"
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                ANALYTICS
              </TabsTrigger>
              <TabsTrigger 
                value="system" 
                className="data-[state=active]:bg-retro-green data-[state=active]:text-black text-retro-green"
              >
                <Settings className="w-4 h-4 mr-2" />
                SYSTEM
              </TabsTrigger>
            </TabsList>

            <TabsContent value="payments" className="mt-6">
              <PaymentManagement />
            </TabsContent>

            <TabsContent value="users" className="mt-6">
              <TerminalWindow title="USER_MANAGEMENT.EXE">
                <div className="text-center text-retro-green/70 py-8">
                  User management module coming soon...
                </div>
              </TerminalWindow>
            </TabsContent>

            <TabsContent value="analytics" className="mt-6">
              <TerminalWindow title="ANALYTICS_ENGINE.EXE">
                <div className="text-center text-retro-green/70 py-8">
                  Advanced analytics dashboard coming soon...
                </div>
              </TerminalWindow>
            </TabsContent>

            <TabsContent value="system" className="mt-6">
              <TerminalWindow title="SYSTEM_CONFIG.INI">
                <div className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="border border-retro-green/50 p-4">
                      <h3 className="text-retro-green font-bold mb-3 flex items-center">
                        <Database className="w-5 h-5 mr-2" />
                        DATABASE STATUS
                      </h3>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-retro-green/80">Connection:</span>
                          <Badge className="bg-green-500 text-black">ACTIVE</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-retro-green/80">Tables:</span>
                          <span className="text-retro-green">4</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-retro-green/80">RLS Policies:</span>
                          <Badge className="bg-green-500 text-black">ENABLED</Badge>
                        </div>
                      </div>
                    </div>

                    <div className="border border-retro-green/50 p-4">
                      <h3 className="text-retro-green font-bold mb-3 flex items-center">
                        <CreditCard className="w-5 h-5 mr-2" />
                        PAYMENT GATEWAY
                      </h3>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-retro-green/80">Stripe:</span>
                          <Badge className="bg-green-500 text-black">CONNECTED</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-retro-green/80">Webhooks:</span>
                          <Badge className="bg-green-500 text-black">ACTIVE</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-retro-green/80">Test Mode:</span>
                          <Badge className="bg-yellow-500 text-black">ENABLED</Badge>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="border border-retro-green/50 p-4">
                    <h3 className="text-retro-green font-bold mb-3">SYSTEM ACTIONS</h3>
                    <div className="grid md:grid-cols-3 gap-4">
                      <Button
                        variant="outline"
                        className="border-retro-green text-retro-green hover:bg-retro-green hover:text-black"
                      >
                        BACKUP DATABASE
                      </Button>
                      <Button
                        variant="outline"
                        className="border-retro-green text-retro-green hover:bg-retro-green hover:text-black"
                      >
                        CLEAR CACHE
                      </Button>
                      <Button
                        variant="outline"
                        className="border-retro-green text-retro-green hover:bg-retro-green hover:text-black"
                      >
                        EXPORT LOGS
                      </Button>
                    </div>
                  </div>
                </div>
              </TerminalWindow>
            </TabsContent>
          </Tabs>
        </TerminalWindow>
      </div>
    </div>
  );
};

export default AdminPanel;
